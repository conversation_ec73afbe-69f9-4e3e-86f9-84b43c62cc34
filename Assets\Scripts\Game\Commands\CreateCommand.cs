using System;
using System.Collections;
using Game.Managers;
using SmartVertex.Tools;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to create a new object or character.
    /// </summary>
    public class CreateCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the create command.</summary>
        public CreateParams Parameters { get; }

        /// <summary>Creates a new CreateCommand.</summary>
        /// <param name="parameters">Create parameters.</param>
        public CreateCommand(CreateParams parameters)
        {
            Parameters = parameters;
            GridUtility.UseOneBasedIndexing = true;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            switch (Parameters.creationMode)
            {
                case CreationMode.Scene:
                    ObjectManager.Instance.CreateScene(
                        Parameters.instanceId,
                        Parameters.prefabAddress);
                    break;
                case CreationMode.Object:
                    ObjectManager.Instance.CreateObject(
                        Parameters.sceneId,
                        Parameters.instanceId,
                        Parameters.prefabAddress,
                        Parameters.gridPosition);
                    break;
                case CreationMode.Character:
                    ObjectManager.Instance.CreateCharacter(
                        Parameters.sceneId,
                        Parameters.instanceId,
                        Parameters.prefabAddress,
                        Parameters.gridPosition);
                    break;
                default:
                    Debug.LogError($"Unknown creation mode: {Parameters.creationMode}");
                    break;
            }
            yield break;
        }
    }

    /// <summary>Parameters for Create command.</summary>
    [Serializable]
    public struct CreateParams
    {
        /// <summary>The type of object to create.</summary>
        public CreationMode creationMode;

        /// <summary>The unique identifier to assign to the created instance (object, character, or scene).</summary>
        public string instanceId;

        /// <summary>The addressable prefab address to instantiate.</summary>
        public string prefabAddress;

        /// <summary>The grid position for object/character placement.</summary>
        public Vector2Int gridPosition;

        /// <summary>The scene identifier where the object should be created (not used for scene creation).</summary>
        public string sceneId;
    }

    /// <summary>
    /// Defines the types of objects that can be created.
    /// </summary>
    public enum CreationMode
    {
        /// <summary>Create a new scene.</summary>
        Scene = 0,
        /// <summary>Create a new object.</summary>
        Object = 1,
        /// <summary>Create a new character.</summary>
        Character = 2
    }
}
