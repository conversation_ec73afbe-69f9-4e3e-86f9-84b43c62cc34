using System.Collections.Generic;
using Game.Objects;
using Game.CommandSystem;
using SmartVertex.Tools;
using TMPro;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Managers
{
    /// <summary>
    /// Manages all audio playback, including dialogue, global audio, and music.
    /// </summary>
    public class AudioManager : Singleton<AudioManager>
    {
        // Fields
        [SerializeField] private TextMeshProUGUI dialogueText;
        [SerializeField] private AudioSource globalAudio;
        [SerializeField] private AudioSource musicAudio;
        [SerializeField] private string apiKey;
        [SerializeField] private string apiUrl;
        [SerializeField] private string ttsModel = "gpt-4o-mini-tts";
        private readonly OpenAITextToSpeech tts = new();
        private readonly AddressablesHelper addressablesHelper = new AddressablesHelper();
        private readonly Dictionary<(string characterId, string text), AudioClip> dialogueClipCache = new();
        private readonly Dictionary<string, AudioClip> audioClipCache = new();

        /// <summary>
        /// Gets or sets the API key for TTS requests.
        /// </summary>
        public string ApiKey
        {
            get => apiKey;
            set => apiKey = value;
        }

        /// <summary>
        /// Gets or sets the API URL for TTS requests.
        /// </summary>
        public string ApiUrl
        {
            get => apiUrl;
            set => apiUrl = value;
        }

        /// <summary>
        /// Gets or sets the TTS model name.
        /// </summary>
        public string TtsModel
        {
            get => ttsModel;
            set => ttsModel = value;
        }

        /// <inheritdoc/>
        protected override void Awake()
        {
            base.Awake();
            globalAudio.loop = false;
            musicAudio.loop = true;
            globalAudio.playOnAwake = false;
            musicAudio.playOnAwake = false;
        }

        /// <inheritdoc/>
        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (dialogueText != null)
            {
                dialogueText.text = string.Empty;
            }
            globalAudio.Stop();
            musicAudio.Stop();
            addressablesHelper.ReleaseAllAssets();
        }

        private async Awaitable<AudioClip> CacheAudioClip(string clipAddress)
        {
            if (audioClipCache.ContainsKey(clipAddress))
            {
                return audioClipCache[clipAddress];
            }
            AudioClip clip = await addressablesHelper.LoadAssetAsync<AudioClip>(clipAddress);
            audioClipCache[clipAddress] = clip;
            return clip;
        }

        private async Awaitable<AudioClip> CacheDialogue(string text, string characterId)
        {
            var key = (characterId, text);
            if (dialogueClipCache.ContainsKey(key))
            {
                return dialogueClipCache[key];
            }

            if (dialogueText != null)
            {
                dialogueText.text = text;
            }

            var characterObject = ObjectManager.Instance.FindCharacter(characterId);
            if (characterObject == null)
            {
                Debug.LogError($"[{nameof(AudioManager)}] Character with ID '{characterId}' not found for dialogue caching.");
                return null;
            }

            var character = characterObject.GetComponentInChildren<Character>();
            if (character == null)
            {
                Debug.LogError($"[{nameof(AudioManager)}] Character component not found on object '{characterId}' for dialogue caching.");
                return null;
            }

            var requestPayload = new OpenAITextToSpeech.TTSRequest
            {
                model = ttsModel,
                input = text,
                voice = character.VoiceActor,
                instructions = character.VoiceInstructions,
                speed = character.VoiceSpeed,
            };
            tts.RequestPayload = requestPayload;
            tts.ApiKey = apiKey;
            tts.ApiUrl = apiUrl;
            AudioClip dialogueClip = await tts.Generate();
            dialogueClipCache[key] = dialogueClip;
            return dialogueClip;
        }

        /// <summary>
        /// Generates and caches audio clips for the provided commands.
        /// </summary>
        /// <param name="commands">List of coroutine commands.</param>
        public async Awaitable GenerateCache(List<ICoroutineCommand> commands)
        {
            foreach (var command in commands)
            {
                if (command is PlaySoundCommand playSoundCommand)
                {
                    if (!audioClipCache.ContainsKey(playSoundCommand.Parameters.clipAddress))
                    {
                        AudioClip clip = await CacheAudioClip(playSoundCommand.Parameters.clipAddress);
                        audioClipCache[playSoundCommand.Parameters.clipAddress] = clip;
                    }
                }
                else if (command is DialogueCommand dialogueCommand)
                {
                    var key = (dialogueCommand.Parameters.characterId, dialogueCommand.Parameters.text);
                    if (!dialogueClipCache.ContainsKey(key))
                    {
                        AudioClip clip = await CacheDialogue(dialogueCommand.Parameters.text, dialogueCommand.Parameters.characterId);
                        dialogueClipCache[key] = clip;
                    }
                }
                else if (command is CommandSystem.CompositeCommand compositeCommand)
                {
                    await GenerateCache(compositeCommand.Commands);
                }
            }
        }

        /// <summary>
        /// Plays a dialogue line for a character using TTS and returns the duration.
        /// </summary>
        /// <param name="text">The dialogue text.</param>
        /// <param name="characterId">The character's ID.</param>
        /// <param name="getDurationCallback">Callback to receive the duration of the dialogue clip.</param>
        public void PlayDialogue(string text, string characterId, UnityAction<float> getDurationCallback = null)
        {
            if (string.IsNullOrEmpty(text))
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Dialogue text is empty.");
                return;
            }
            if (string.IsNullOrEmpty(characterId))
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Character ID is empty.");
                return;
            }

            var characterObject = ObjectManager.Instance.FindCharacter(characterId);
            if (characterObject == null)
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Character with ID '{characterId}' not found.");
                return;
            }

            var character = characterObject.GetComponentInChildren<Character>();
            if (character == null)
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Character component not found on object '{characterId}'.");
                return;
            }

            var key = (characterId, text);
            if (!dialogueClipCache.TryGetValue(key, out var dialogueClip))
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Dialogue clip not found in cache for character '{characterId}' and text '{text}'.");
                return;
            }

            character.PlayDialogue(dialogueClip);
            getDurationCallback?.Invoke(dialogueClip.length);
        }

        /// <summary>
        /// Plays a global audio clip.
        /// </summary>
        /// <param name="clipAddress">The address of the audio clip.</param>
        /// <param name="volume">The volume to play at.</param>
        /// <param name="pitch">The pitch to play at.</param>
        public void PlayAudio(string clipAddress, float volume = 1, float pitch = 1)
        {
            if (!audioClipCache.TryGetValue(clipAddress, out var clip))
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Audio clip '{clipAddress}' not found in cache.");
                return;
            }

            globalAudio.clip = clip;
            globalAudio.volume = volume;
            globalAudio.pitch = pitch;
            globalAudio.Play();
        }

        /// <summary>
        /// Plays a music audio clip.
        /// </summary>
        /// <param name="clipAddress">The address of the music clip.</param>
        /// <param name="volume">The volume to play at.</param>
        /// <param name="pitch">The pitch to play at.</param>
        public void PlayMusic(string clipAddress, float volume = 0.5f, float pitch = 1)
        {
            if (!audioClipCache.TryGetValue(clipAddress, out var clip))
            {
                Debug.LogWarning($"[{nameof(AudioManager)}] Music clip '{clipAddress}' not found in cache.");
                return;
            }

            musicAudio.clip = clip;
            musicAudio.volume = volume;
            musicAudio.pitch = pitch;
            musicAudio.Play();
        }
    }
}